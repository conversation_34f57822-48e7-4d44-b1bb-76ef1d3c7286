/**
* This file is part of ORB-SLAM3
*
* Copyright (C) 2017-2021 <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, University of Zaragoza.
* Copyright (C) 2014-2016 <PERSON><PERSON>, <PERSON> and <PERSON>, University of Zaragoza.
*
* ORB-SLAM3 is free software: you can redistribute it and/or modify it under the terms of the GNU General Public
* License as published by the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* ORB-SLAM3 is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even
* the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License along with ORB-SLAM3.
* If not, see <http://www.gnu.org/licenses/>.
*/

#include "FrameDrawer.h"
#include "Tracking.h"

#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>

#include<mutex>
#include<cfloat>
#include<cmath>
#include<algorithm>

namespace ORB_SLAM3
{

FrameDrawer::FrameDrawer(Atlas* pAtlas):both(false),mpAtlas(pAtlas)
{
    mState=Tracking::SYSTEM_NOT_READY;
    mIm = cv::Mat(480,640,CV_8UC3, cv::Scalar(0,0,0));
    mImRight = cv::Mat(480,640,CV_8UC3, cv::Scalar(0,0,0));

    // 【新增】初始化距离可视化配置参数（默认值）
    mbUseAbsoluteDistance = false;
    mMinDistance = 0.5f;
    mMaxDistance = 10.0f;
}

FrameDrawer::FrameDrawer(Atlas* pAtlas, Settings* settings):both(false),mpAtlas(pAtlas)
{
    mState=Tracking::SYSTEM_NOT_READY;
    mIm = cv::Mat(480,640,CV_8UC3, cv::Scalar(0,0,0));
    mImRight = cv::Mat(480,640,CV_8UC3, cv::Scalar(0,0,0));

    // 【新增】从Settings加载距离可视化配置参数
    if(settings) {
        mbUseAbsoluteDistance = settings->useAbsoluteDistance();
        mMinDistance = settings->minDistance();
        mMaxDistance = settings->maxDistance();
    } else {
        mbUseAbsoluteDistance = false;
        mMinDistance = 0.5f;
        mMaxDistance = 10.0f;
    }
}

cv::Mat FrameDrawer::DrawFrame(float imageScale)
{
    cv::Mat im;
    vector<cv::KeyPoint> vIniKeys; // Initialization: KeyPoints in reference frame
    vector<int> vMatches; // Initialization: correspondeces with reference keypoints
    vector<cv::KeyPoint> vCurrentKeys; // KeyPoints in current frame
    vector<bool> vbVO, vbMap; // Tracked MapPoints in current frame
    vector<pair<cv::Point2f, cv::Point2f> > vTracks;
    int state; // Tracking state
    vector<float> vCurrentDepth;
    float thDepth;

    Frame currentFrame;
    vector<MapPoint*> vpLocalMap;
    vector<cv::KeyPoint> vMatchesKeys;
    vector<MapPoint*> vpMatchedMPs;
    vector<cv::KeyPoint> vOutlierKeys;
    vector<MapPoint*> vpOutlierMPs;
    map<long unsigned int, cv::Point2f> mProjectPoints;
    map<long unsigned int, cv::Point2f> mMatchedInImage;

    cv::Scalar standardColor(0,255,0);
    cv::Scalar odometryColor(255,0,0);

    //Copy variables within scoped mutex
    {
        unique_lock<mutex> lock(mMutex);
        state=mState;
        if(mState==Tracking::SYSTEM_NOT_READY)
            mState=Tracking::NO_IMAGES_YET;

        mIm.copyTo(im);

        if(mState==Tracking::NOT_INITIALIZED)
        {
            vCurrentKeys = mvCurrentKeys;
            vIniKeys = mvIniKeys;
            vMatches = mvIniMatches;
            vTracks = mvTracks;
        }
        else if(mState==Tracking::OK)
        {
            vCurrentKeys = mvCurrentKeys;
            vbVO = mvbVO;
            vbMap = mvbMap;

            currentFrame = mCurrentFrame;
            vpLocalMap = mvpLocalMap;
            vMatchesKeys = mvMatchedKeys;
            vpMatchedMPs = mvpMatchedMPs;
            vOutlierKeys = mvOutlierKeys;
            vpOutlierMPs = mvpOutlierMPs;
            mProjectPoints = mmProjectPoints;
            mMatchedInImage = mmMatchedInImage;

            vCurrentDepth = mvCurrentDepth;
            thDepth = mThDepth;

        }
        else if(mState==Tracking::LOST)
        {
            vCurrentKeys = mvCurrentKeys;
        }
    }

    if(imageScale != 1.f)
    {
        int imWidth = im.cols / imageScale;
        int imHeight = im.rows / imageScale;
        cv::resize(im, im, cv::Size(imWidth, imHeight));
    }

    if(im.channels()<3) //this should be always true
        cvtColor(im,im,cv::COLOR_GRAY2BGR);

    //Draw
    if(state==Tracking::NOT_INITIALIZED)
    {
        for(unsigned int i=0; i<vMatches.size(); i++)
        {
            if(vMatches[i]>=0)
            {
                cv::Point2f pt1,pt2;
                if(imageScale != 1.f)
                {
                    pt1 = vIniKeys[i].pt / imageScale;
                    pt2 = vCurrentKeys[vMatches[i]].pt / imageScale;
                }
                else
                {
                    pt1 = vIniKeys[i].pt;
                    pt2 = vCurrentKeys[vMatches[i]].pt;
                }
                cv::line(im,pt1,pt2,standardColor);
            }
        }
        for(vector<pair<cv::Point2f, cv::Point2f> >::iterator it=vTracks.begin(); it!=vTracks.end(); it++)
        {
            cv::Point2f pt1,pt2;
            if(imageScale != 1.f)
            {
                pt1 = (*it).first / imageScale;
                pt2 = (*it).second / imageScale;
            }
            else
            {
                pt1 = (*it).first;
                pt2 = (*it).second;
            }
            cv::line(im,pt1,pt2, standardColor,5);
        }

    }
    else if(state==Tracking::OK) //TRACKING
    {
        mnTracked=0;
        mnTrackedVO=0;
        const float r = 5;
        int n = vCurrentKeys.size();

        // 【修改】根据配置选择距离范围计算方式
        Eigen::Vector3f currentCameraPos = getCurrentCameraPosition();
        std::set<MapPoint*> visiblePoints = getCurrentFrameVisibleMapPoints();
        DistanceRange distRange;
        if(!mbUseAbsoluteDistance) {
            // 相对距离模式：计算当前帧可见地图点的距离范围
            distRange = calculateVisiblePointsDistanceRange(visiblePoints, currentCameraPos);
        }

        for(int i=0;i<n;i++)
        {
            if(vbVO[i] || vbMap[i])
            {
                cv::Point2f pt1,pt2;
                cv::Point2f point;
                if(imageScale != 1.f)
                {
                    point = vCurrentKeys[i].pt / imageScale;
                    float px = vCurrentKeys[i].pt.x / imageScale;
                    float py = vCurrentKeys[i].pt.y / imageScale;
                    pt1.x=px-r;
                    pt1.y=py-r;
                    pt2.x=px+r;
                    pt2.y=py+r;
                }
                else
                {
                    point = vCurrentKeys[i].pt;
                    pt1.x=vCurrentKeys[i].pt.x-r;
                    pt1.y=vCurrentKeys[i].pt.y-r;
                    pt2.x=vCurrentKeys[i].pt.x+r;
                    pt2.y=vCurrentKeys[i].pt.y+r;
                }

                // This is a match to a MapPoint in the map
                if(vbMap[i])
                {
                    // 【修改】根据配置选择颜色计算方式
                    cv::Scalar pointColor = standardColor;  // 默认绿色

                    MapPoint* pMP = currentFrame.mvpMapPoints[i];
                    if(pMP && !pMP->isBad()) {
                        Eigen::Vector3f pos = pMP->GetWorldPos();
                        float distance = (pos - currentCameraPos).norm();

                        if(mbUseAbsoluteDistance) {
                            pointColor = getColorByAbsoluteDistance(distance);
                        } else if(distRange.isValid()) {
                            pointColor = getColorByRelativeDistance(distance, distRange);
                        }
                    }

                    cv::rectangle(im,pt1,pt2,pointColor);
                    cv::circle(im,point,2,pointColor,-1);
                    mnTracked++;
                }
                else // This is match to a "visual odometry" MapPoint created in the last frame
                {
                    cv::rectangle(im,pt1,pt2,odometryColor);
                    cv::circle(im,point,2,odometryColor,-1);
                    mnTrackedVO++;
                }
            }
        }
    }

    cv::Mat imWithInfo;
    DrawTextInfo(im,state, imWithInfo);

    return imWithInfo;
}

cv::Mat FrameDrawer::DrawRightFrame(float imageScale)
{
    cv::Mat im;
    vector<cv::KeyPoint> vIniKeys; // Initialization: KeyPoints in reference frame
    vector<int> vMatches; // Initialization: correspondeces with reference keypoints
    vector<cv::KeyPoint> vCurrentKeys; // KeyPoints in current frame
    vector<bool> vbVO, vbMap; // Tracked MapPoints in current frame
    int state; // Tracking state

    //Copy variables within scoped mutex
    {
        unique_lock<mutex> lock(mMutex);
        state=mState;
        if(mState==Tracking::SYSTEM_NOT_READY)
            mState=Tracking::NO_IMAGES_YET;

        mImRight.copyTo(im);

        if(mState==Tracking::NOT_INITIALIZED)
        {
            vCurrentKeys = mvCurrentKeysRight;
            vIniKeys = mvIniKeys;
            vMatches = mvIniMatches;
        }
        else if(mState==Tracking::OK)
        {
            vCurrentKeys = mvCurrentKeysRight;
            vbVO = mvbVO;
            vbMap = mvbMap;
        }
        else if(mState==Tracking::LOST)
        {
            vCurrentKeys = mvCurrentKeysRight;
        }
    } // destroy scoped mutex -> release mutex

    if(imageScale != 1.f)
    {
        int imWidth = im.cols / imageScale;
        int imHeight = im.rows / imageScale;
        cv::resize(im, im, cv::Size(imWidth, imHeight));
    }

    if(im.channels()<3) //this should be always true
        cvtColor(im,im,cv::COLOR_GRAY2BGR);

    //Draw
    if(state==Tracking::NOT_INITIALIZED) //INITIALIZING
    {
        for(unsigned int i=0; i<vMatches.size(); i++)
        {
            if(vMatches[i]>=0)
            {
                cv::Point2f pt1,pt2;
                if(imageScale != 1.f)
                {
                    pt1 = vIniKeys[i].pt / imageScale;
                    pt2 = vCurrentKeys[vMatches[i]].pt / imageScale;
                }
                else
                {
                    pt1 = vIniKeys[i].pt;
                    pt2 = vCurrentKeys[vMatches[i]].pt;
                }

                cv::line(im,pt1,pt2,cv::Scalar(0,255,0));
            }
        }
    }
    else if(state==Tracking::OK) //TRACKING
    {
        mnTracked=0;
        mnTrackedVO=0;
        const float r = 5;
        const int n = mvCurrentKeysRight.size();
        const int Nleft = mvCurrentKeys.size();

        for(int i=0;i<n;i++)
        {
            if(vbVO[i + Nleft] || vbMap[i + Nleft])
            {
                cv::Point2f pt1,pt2;
                cv::Point2f point;
                if(imageScale != 1.f)
                {
                    point = mvCurrentKeysRight[i].pt / imageScale;
                    float px = mvCurrentKeysRight[i].pt.x / imageScale;
                    float py = mvCurrentKeysRight[i].pt.y / imageScale;
                    pt1.x=px-r;
                    pt1.y=py-r;
                    pt2.x=px+r;
                    pt2.y=py+r;
                }
                else
                {
                    point = mvCurrentKeysRight[i].pt;
                    pt1.x=mvCurrentKeysRight[i].pt.x-r;
                    pt1.y=mvCurrentKeysRight[i].pt.y-r;
                    pt2.x=mvCurrentKeysRight[i].pt.x+r;
                    pt2.y=mvCurrentKeysRight[i].pt.y+r;
                }

                // This is a match to a MapPoint in the map
                if(vbMap[i + Nleft])
                {
                    cv::rectangle(im,pt1,pt2,cv::Scalar(0,255,0));
                    cv::circle(im,point,2,cv::Scalar(0,255,0),-1);
                    mnTracked++;
                }
                else // This is match to a "visual odometry" MapPoint created in the last frame
                {
                    cv::rectangle(im,pt1,pt2,cv::Scalar(255,0,0));
                    cv::circle(im,point,2,cv::Scalar(255,0,0),-1);
                    mnTrackedVO++;
                }
            }
        }
    }

    cv::Mat imWithInfo;
    DrawTextInfo(im,state, imWithInfo);

    return imWithInfo;
}



void FrameDrawer::DrawTextInfo(cv::Mat &im, int nState, cv::Mat &imText)
{
    stringstream s;
    if(nState==Tracking::NO_IMAGES_YET)
        s << " WAITING FOR IMAGES";
    else if(nState==Tracking::NOT_INITIALIZED)
        s << " TRYING TO INITIALIZE ";
    else if(nState==Tracking::OK)
    {
        if(!mbOnlyTracking)
            s << "SLAM MODE |  ";
        else
            s << "LOCALIZATION | ";
        int nMaps = mpAtlas->CountMaps();
        int nKFs = mpAtlas->KeyFramesInMap();
        int nMPs = mpAtlas->MapPointsInMap();
        s << "Maps: " << nMaps << ", KFs: " << nKFs << ", MPs: " << nMPs << ", Matches: " << mnTracked;
        if(mnTrackedVO>0)
            s << ", + VO matches: " << mnTrackedVO;
    }
    else if(nState==Tracking::LOST)
    {
        s << " TRACK LOST. TRYING TO RELOCALIZE ";
    }
    else if(nState==Tracking::SYSTEM_NOT_READY)
    {
        s << " LOADING ORB VOCABULARY. PLEASE WAIT...";
    }

    int baseline=0;
    cv::Size textSize = cv::getTextSize(s.str(),cv::FONT_HERSHEY_PLAIN,1,1,&baseline);

    imText = cv::Mat(im.rows+textSize.height+10,im.cols,im.type());
    im.copyTo(imText.rowRange(0,im.rows).colRange(0,im.cols));
    imText.rowRange(im.rows,imText.rows) = cv::Mat::zeros(textSize.height+10,im.cols,im.type());
    cv::putText(imText,s.str(),cv::Point(5,imText.rows-5),cv::FONT_HERSHEY_PLAIN,1,cv::Scalar(255,255,255),1,8);

}

void FrameDrawer::Update(Tracking *pTracker)
{
    unique_lock<mutex> lock(mMutex);
    pTracker->mImGray.copyTo(mIm);
    mvCurrentKeys=pTracker->mCurrentFrame.mvKeys;
    mThDepth = pTracker->mCurrentFrame.mThDepth;
    mvCurrentDepth = pTracker->mCurrentFrame.mvDepth;

    if(both){
        mvCurrentKeysRight = pTracker->mCurrentFrame.mvKeysRight;
        pTracker->mImRight.copyTo(mImRight);
        N = mvCurrentKeys.size() + mvCurrentKeysRight.size();
    }
    else{
        N = mvCurrentKeys.size();
    }

    mvbVO = vector<bool>(N,false);
    mvbMap = vector<bool>(N,false);
    mbOnlyTracking = pTracker->mbOnlyTracking;

    //Variables for the new visualization
    mCurrentFrame = pTracker->mCurrentFrame;
    mmProjectPoints = mCurrentFrame.mmProjectPoints;
    mmMatchedInImage.clear();

    mvpLocalMap = pTracker->GetLocalMapMPS();
    mvMatchedKeys.clear();
    mvMatchedKeys.reserve(N);
    mvpMatchedMPs.clear();
    mvpMatchedMPs.reserve(N);
    mvOutlierKeys.clear();
    mvOutlierKeys.reserve(N);
    mvpOutlierMPs.clear();
    mvpOutlierMPs.reserve(N);

    if(pTracker->mLastProcessedState==Tracking::NOT_INITIALIZED)
    {
        mvIniKeys=pTracker->mInitialFrame.mvKeys;
        mvIniMatches=pTracker->mvIniMatches;
    }
    else if(pTracker->mLastProcessedState==Tracking::OK)
    {
        for(int i=0;i<N;i++)
        {
            MapPoint* pMP = pTracker->mCurrentFrame.mvpMapPoints[i];
            if(pMP)
            {
                if(!pTracker->mCurrentFrame.mvbOutlier[i])
                {
                    if(pMP->Observations()>0)
                        mvbMap[i]=true;
                    else
                        mvbVO[i]=true;

                    mmMatchedInImage[pMP->mnId] = mvCurrentKeys[i].pt;
                }
                else
                {
                    mvpOutlierMPs.push_back(pMP);
                    mvOutlierKeys.push_back(mvCurrentKeys[i]);
                }
            }
        }

    }
    mState=static_cast<int>(pTracker->mLastProcessedState);
}

// 【新增】获取当前相机位置
Eigen::Vector3f FrameDrawer::getCurrentCameraPosition()
{
    unique_lock<mutex> lock(mMutex);

    if(mState != Tracking::OK) {
        return Eigen::Vector3f::Zero();
    }

    // 从当前帧获取相机位置
    Sophus::SE3f Tcw = mCurrentFrame.GetPose();
    Eigen::Matrix3f Rcw = Tcw.rotationMatrix();
    Eigen::Vector3f tcw = Tcw.translation();

    // 计算相机在世界坐标系中的位置
    Eigen::Vector3f Ow = -Rcw.transpose() * tcw;

    return Ow;
}

// 【新增】获取当前帧可见的地图点
std::set<MapPoint*> FrameDrawer::getCurrentFrameVisibleMapPoints()
{
    unique_lock<mutex> lock(mMutex);
    std::set<MapPoint*> visiblePoints;

    if(mState != Tracking::OK) {
        return visiblePoints;
    }

    // 遍历当前帧的所有地图点
    for(int i = 0; i < N; i++) {
        if(mvbMap[i]) {  // 只考虑真正的地图点（不包括VO点）
            MapPoint* pMP = mCurrentFrame.mvpMapPoints[i];
            if(pMP && !pMP->isBad()) {
                visiblePoints.insert(pMP);
            }
        }
    }

    return visiblePoints;
}

// 【新增】计算可见地图点的距离范围
FrameDrawer::DistanceRange FrameDrawer::calculateVisiblePointsDistanceRange(
    const std::set<MapPoint*>& visiblePoints,
    const Eigen::Vector3f& cameraPos)
{
    DistanceRange distRange;

    if(visiblePoints.empty()) {
        return distRange;
    }

    float minDist = FLT_MAX;
    float maxDist = 0.0f;

    for(MapPoint* pMP : visiblePoints) {
        if(!pMP || pMP->isBad()) continue;

        Eigen::Vector3f pos = pMP->GetWorldPos();
        float distance = (pos - cameraPos).norm();

        minDist = std::min(minDist, distance);
        maxDist = std::max(maxDist, distance);
    }

    distRange.minDistance = minDist;
    distRange.maxDistance = maxDist;
    distRange.range = maxDist - minDist;

    return distRange;
}

// 【新增】根据相对距离获取OpenCV颜色（BGR格式）
cv::Scalar FrameDrawer::getColorByRelativeDistance(float distance, const DistanceRange& distRange)
{
    // 如果距离范围无效或距离无效，使用默认绿色
    if(!distRange.isValid() || distance <= 0.0f || !std::isfinite(distance)) {
        return cv::Scalar(0, 255, 0);  // 绿色 (BGR)
    }

    // 计算相对位置 (0.0 到 1.0)
    float relativePosition = (distance - distRange.minDistance) / distRange.range;

    // 确保在有效范围内
    relativePosition = std::max(0.0f, std::min(1.0f, relativePosition));

    // 靛紫到春绿渐变：最近的点显示靛紫色，最远的点显示春绿色
    // relativePosition = 0.0 (最近) -> 靛紫色 #4B0082 (RGB: 75, 0, 130)
    // relativePosition = 1.0 (最远) -> 春绿色 #00FF7F (RGB: 0, 255, 127)

    float nearR = 75.0f;   // 靛紫色 R
    float nearG = 0.0f;    // 靛紫色 G
    float nearB = 130.0f;  // 靛紫色 B

    float farR = 0.0f;     // 春绿色 R
    float farG = 255.0f;   // 春绿色 G
    float farB = 127.0f;   // 春绿色 B

    // 线性插值计算当前颜色
    float r = nearR + relativePosition * (farR - nearR);
    float g = nearG + relativePosition * (farG - nearG);
    float b = nearB + relativePosition * (farB - nearB);

    // OpenCV使用BGR格式
    return cv::Scalar(b, g, r);
}

// 【新增】根据绝对距离获取OpenCV颜色（BGR格式）
cv::Scalar FrameDrawer::getColorByAbsoluteDistance(float distance)
{
    // 颜色定义：靛紫色到春绿色的渐变
    // 靛紫色 (Indigo): RGB(75, 0, 130) -> BGR(130, 0, 75)
    // 春绿色 (Spring Green): RGB(0, 255, 127) -> BGR(127, 255, 0)

    float ratio;

    if(distance <= mMinDistance) {
        // 距离小于等于最小距离：使用靛紫色
        ratio = 0.0f;
    } else if(distance >= mMaxDistance) {
        // 距离大于等于最大距离：使用春绿色
        ratio = 1.0f;
    } else {
        // 在范围内：线性插值
        ratio = (distance - mMinDistance) / (mMaxDistance - mMinDistance);
    }

    // 线性插值计算颜色（BGR格式）
    int b = (int)(130 * (1.0f - ratio) + 127 * ratio);  // 蓝色分量
    int g = (int)(0 * (1.0f - ratio) + 255 * ratio);    // 绿色分量
    int r = (int)(75 * (1.0f - ratio) + 0 * ratio);     // 红色分量

    return cv::Scalar(b, g, r);
}

} //namespace ORB_SLAM
