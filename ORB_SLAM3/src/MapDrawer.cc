/**
* This file is part of ORB-SLAM3
*
* Copyright (C) 2017-2021 <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, University of Zaragoza.
* Copyright (C) 2014-2016 <PERSON><PERSON>, <PERSON> and <PERSON>, University of Zaragoza.
*
* ORB-SLAM3 is free software: you can redistribute it and/or modify it under the terms of the GNU General Public
* License as published by the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* ORB-SLAM3 is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even
* the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License along with ORB-SLAM3.
* If not, see <http://www.gnu.org/licenses/>.
*/

#include "MapDrawer.h"
#include "MapPoint.h"
#include "KeyFrame.h"
#include <pangolin/pangolin.h>
#include <mutex>
#include <set>
#include <cfloat>
#include <algorithm>
#include <fstream>
#include <iomanip>
#include <chrono>
#include <utility>
#include <iostream>
#include <iostream>

namespace ORB_SLAM3
{


MapDrawer::MapDrawer(Atlas* pAtlas, const string &strSettingPath, Settings* settings):mpAtlas(pAtlas), mbRecordDistances(false), mCurrentFrameId(0), mCurrentTimestamp(0.0)
{
    if(settings){
        newParameterLoader(settings);
    }
    else{
        cv::FileStorage fSettings(strSettingPath, cv::FileStorage::READ);
        bool is_correct = ParseViewerParamFile(fSettings);

        if(!is_correct)
        {
            std::cerr << "**ERROR in the config file, the format is not correct**" << std::endl;
            try
            {
                throw -1;
            }
            catch(exception &e)
            {

            }
        }

        // 【新增】读取相机内参和原始图像尺寸
        LoadCameraParameters(fSettings);
    }
}

void MapDrawer::newParameterLoader(Settings *settings) {
    mKeyFrameSize = settings->keyFrameSize();
    mKeyFrameLineWidth = settings->keyFrameLineWidth();
    mGraphLineWidth = settings->graphLineWidth();
    mPointSize = settings->pointSize();
    mCameraSize = settings->cameraSize();
    mCameraLineWidth  = settings->cameraLineWidth();

    // 【新增】从Settings获取相机参数
    GeometricCamera* pCamera = settings->camera1();
    if(pCamera) {
        // 对于Pinhole相机，参数顺序是 [fx, fy, cx, cy]
        mCameraFx = pCamera->getParameter(0);  // fx
        mCameraFy = pCamera->getParameter(1);  // fy
        mCameraCx = pCamera->getParameter(2);  // cx
        mCameraCy = pCamera->getParameter(3);  // cy
    } else {
        // 默认值（如果无法获取相机参数）
        mCameraFx = 500.0f;
        mCameraFy = 500.0f;
        mCameraCx = 320.0f;
        mCameraCy = 240.0f;
        std::cerr << "*Warning: Could not get camera parameters from Settings, using default values*" << std::endl;
    }

    // 图像尺寸
    cv::Size imgSize = settings->newImSize();
    mCameraWidth = imgSize.width;
    mCameraHeight = imgSize.height;

    // 【修复】设置原始图像尺寸用于像素坐标计算
    // 由于Settings类没有公共方法获取原始图像尺寸，我们使用缩放后的尺寸
    // 这意味着归一化是基于缩放后的图像进行的
    mOriginalImageWidth = imgSize.width;
    mOriginalImageHeight = imgSize.height;

    // 视场角使用默认值（Settings类中没有这些参数）
    mCameraHFov = 60.0f;  // 默认水平视场角
    mCameraVFov = 45.0f;  // 默认垂直视场角
    std::cerr << "*Warning: Using default field of view values (60°H, 45°V). Consider adding Camera.hfov and Camera.vfov to config file*" << std::endl;

    // 【新增】加载距离可视化配置参数
    mbUseAbsoluteDistance = settings->useAbsoluteDistance();
    mMinDistance = settings->minDistance();
    mMaxDistance = settings->maxDistance();

    // 【新增】输出相机参数用于调试
    std::cout << "Loaded camera parameters from Settings for distance recording:" << std::endl;
    std::cout << "  fx=" << mCameraFx << ", fy=" << mCameraFy << std::endl;
    std::cout << "  cx=" << mCameraCx << ", cy=" << mCameraCy << std::endl;
    std::cout << "  Original image size: " << mOriginalImageWidth << "x" << mOriginalImageHeight << std::endl;

    // 【新增】输出距离可视化配置
    std::cout << "Distance visualization settings:" << std::endl;
    std::cout << "  Mode: " << (mbUseAbsoluteDistance ? "Absolute distance" : "Relative distance") << std::endl;
    if(mbUseAbsoluteDistance) {
        std::cout << "  Distance range: " << mMinDistance << "m - " << mMaxDistance << "m" << std::endl;
    }
}

bool MapDrawer::ParseViewerParamFile(cv::FileStorage &fSettings)
{
    bool b_miss_params = false;

    cv::FileNode node = fSettings["Viewer.KeyFrameSize"];
    if(!node.empty())
    {
        mKeyFrameSize = node.real();
    }
    else
    {
        std::cerr << "*Viewer.KeyFrameSize parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Viewer.KeyFrameLineWidth"];
    if(!node.empty())
    {
        mKeyFrameLineWidth = node.real();
    }
    else
    {
        std::cerr << "*Viewer.KeyFrameLineWidth parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Viewer.GraphLineWidth"];
    if(!node.empty())
    {
        mGraphLineWidth = node.real();
    }
    else
    {
        std::cerr << "*Viewer.GraphLineWidth parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Viewer.PointSize"];
    if(!node.empty())
    {
        mPointSize = node.real();
    }
    else
    {
        std::cerr << "*Viewer.PointSize parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Viewer.CameraSize"];
    if(!node.empty())
    {
        mCameraSize = node.real();
    }
    else
    {
        std::cerr << "*Viewer.CameraSize parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Viewer.CameraLineWidth"];
    if(!node.empty())
    {
        mCameraLineWidth = node.real();
    }
    else
    {
        std::cerr << "*Viewer.CameraLineWidth parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    // 【新增】读取相机参数（用于可见性判断）
    node = fSettings["Camera.fx"];
    if(!node.empty())
    {
        mCameraFx = node.real();
    }
    else
    {
        std::cerr << "*Camera.fx parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Camera.fy"];
    if(!node.empty())
    {
        mCameraFy = node.real();
    }
    else
    {
        std::cerr << "*Camera.fy parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Camera.cx"];
    if(!node.empty())
    {
        mCameraCx = node.real();
    }
    else
    {
        std::cerr << "*Camera.cx parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Camera.cy"];
    if(!node.empty())
    {
        mCameraCy = node.real();
    }
    else
    {
        std::cerr << "*Camera.cy parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Camera.width"];
    if(!node.empty())
    {
        mCameraWidth = node.real();
    }
    else
    {
        std::cerr << "*Camera.width parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    node = fSettings["Camera.height"];
    if(!node.empty())
    {
        mCameraHeight = node.real();
    }
    else
    {
        std::cerr << "*Camera.height parameter doesn't exist or is not a real number*" << std::endl;
        b_miss_params = true;
    }

    // 【新增】读取视场角参数（可选，有默认值）
    node = fSettings["Camera.hfov"];
    if(!node.empty())
    {
        mCameraHFov = node.real();
    }
    else
    {
        std::cerr << "*Camera.hfov parameter doesn't exist, using default value 60.0 degrees*" << std::endl;
        mCameraHFov = 60.0f;  // 默认水平视场角
    }

    node = fSettings["Camera.vfov"];
    if(!node.empty())
    {
        mCameraVFov = node.real();
    }
    else
    {
        std::cerr << "*Camera.vfov parameter doesn't exist, using default value 45.0 degrees*" << std::endl;
        mCameraVFov = 45.0f;  // 默认垂直视场角
    }

    // 【新增】初始化距离可视化配置参数（使用默认值）
    mbUseAbsoluteDistance = false;  // 默认使用相对距离模式
    mMinDistance = 0.5f;           // 默认最小距离
    mMaxDistance = 10.0f;          // 默认最大距离

    return !b_miss_params;
}

void MapDrawer::DrawMapPoints()
{
    Map* pActiveMap = mpAtlas->GetCurrentMap();
    if(!pActiveMap)
        return;

    const vector<MapPoint*> &vpMPs = pActiveMap->GetAllMapPoints();
    const vector<MapPoint*> &vpRefMPs = pActiveMap->GetReferenceMapPoints();
    set<MapPoint*> spRefMPs(vpRefMPs.begin(), vpRefMPs.end());

    if(vpMPs.empty())
        return;

    // 【新增】获取当前相机位置和可见地图点
    Eigen::Vector3f currentCameraPos = getCurrentCameraPosition();
    std::set<MapPoint*> visiblePoints = getCurrentFrameVisiblePoints();

    // 【修改】根据配置选择距离范围计算方式
    DistanceRange distRange;
    if(!mbUseAbsoluteDistance) {
        // 相对距离模式：计算当前帧可见地图点的距离范围
        distRange = calculateVisiblePointsDistanceRange(visiblePoints, currentCameraPos);
    }

    glPointSize(mPointSize);
    glBegin(GL_POINTS);

    // 处理所有地图点
    for(size_t i=0, iend=vpMPs.size(); i<iend;i++)
    {
        if(vpMPs[i]->isBad())
            continue;

        Eigen::Vector3f pos = vpMPs[i]->GetWorldPos();

        if(visiblePoints.count(vpMPs[i])) {
            // 【修改】当前帧可见：根据配置选择颜色编码方式
            float distance = (pos - currentCameraPos).norm();
            if(mbUseAbsoluteDistance) {
                setVisiblePointColorByAbsoluteDistance(distance);
            } else {
                setVisiblePointColorByRelativeDistance(distance, distRange);
            }
        } else {
            // 当前帧不可见：保持原有语义颜色
            if(spRefMPs.count(vpMPs[i])) {
                glColor3f(1.0f, 0.0f, 0.0f);  // 红色（Reference）
            } else {
                glColor3f(0.0f, 0.0f, 0.0f);  // 黑色（普通）
            }
        }

        glVertex3f(pos(0), pos(1), pos(2));
    }
    glEnd();

    // 【新增】记录距离信息
    RecordCurrentFrameDistances();
}

void MapDrawer::DrawKeyFrames(const bool bDrawKF, const bool bDrawGraph, const bool bDrawInertialGraph, const bool bDrawOptLba)
{
    const float &w = mKeyFrameSize;
    const float h = w*0.75;
    const float z = w*0.6;

    Map* pActiveMap = mpAtlas->GetCurrentMap();
    // DEBUG LBA
    std::set<long unsigned int> sOptKFs = pActiveMap->msOptKFs;
    std::set<long unsigned int> sFixedKFs = pActiveMap->msFixedKFs;

    if(!pActiveMap)
        return;

    const vector<KeyFrame*> vpKFs = pActiveMap->GetAllKeyFrames();

    if(bDrawKF)
    {
        for(size_t i=0; i<vpKFs.size(); i++)
        {
            KeyFrame* pKF = vpKFs[i];
            Eigen::Matrix4f Twc = pKF->GetPoseInverse().matrix();
            unsigned int index_color = pKF->mnOriginMapId;

            glPushMatrix();

            glMultMatrixf((GLfloat*)Twc.data());

            if(!pKF->GetParent()) // It is the first KF in the map
            {
                glLineWidth(mKeyFrameLineWidth*5);
                glColor3f(1.0f,0.0f,0.0f);
                glBegin(GL_LINES);
            }
            else
            {
                //cout << "Child KF: " << vpKFs[i]->mnId << endl;
                glLineWidth(mKeyFrameLineWidth);
                if (bDrawOptLba) {
                    if(sOptKFs.find(pKF->mnId) != sOptKFs.end())
                    {
                        glColor3f(0.0f,1.0f,0.0f); // Green -> Opt KFs
                    }
                    else if(sFixedKFs.find(pKF->mnId) != sFixedKFs.end())
                    {
                        glColor3f(1.0f,0.0f,0.0f); // Red -> Fixed KFs
                    }
                    else
                    {
                        glColor3f(0.0f,0.0f,1.0f); // Basic color
                    }
                }
                else
                {
                    glColor3f(0.0f,0.0f,1.0f); // Basic color
                }
                glBegin(GL_LINES);
            }

            glVertex3f(0,0,0);
            glVertex3f(w,h,z);
            glVertex3f(0,0,0);
            glVertex3f(w,-h,z);
            glVertex3f(0,0,0);
            glVertex3f(-w,-h,z);
            glVertex3f(0,0,0);
            glVertex3f(-w,h,z);

            glVertex3f(w,h,z);
            glVertex3f(w,-h,z);

            glVertex3f(-w,h,z);
            glVertex3f(-w,-h,z);

            glVertex3f(-w,h,z);
            glVertex3f(w,h,z);

            glVertex3f(-w,-h,z);
            glVertex3f(w,-h,z);
            glEnd();

            glPopMatrix();

            glEnd();
        }
    }

    if(bDrawGraph)
    {
        glLineWidth(mGraphLineWidth);
        glColor4f(0.0f,1.0f,0.0f,0.6f);
        glBegin(GL_LINES);

        // cout << "-----------------Draw graph-----------------" << endl;
        for(size_t i=0; i<vpKFs.size(); i++)
        {
            // Covisibility Graph
            const vector<KeyFrame*> vCovKFs = vpKFs[i]->GetCovisiblesByWeight(100);
            Eigen::Vector3f Ow = vpKFs[i]->GetCameraCenter();
            if(!vCovKFs.empty())
            {
                for(vector<KeyFrame*>::const_iterator vit=vCovKFs.begin(), vend=vCovKFs.end(); vit!=vend; vit++)
                {
                    if((*vit)->mnId<vpKFs[i]->mnId)
                        continue;
                    Eigen::Vector3f Ow2 = (*vit)->GetCameraCenter();
                    glVertex3f(Ow(0),Ow(1),Ow(2));
                    glVertex3f(Ow2(0),Ow2(1),Ow2(2));
                }
            }

            // Spanning tree
            KeyFrame* pParent = vpKFs[i]->GetParent();
            if(pParent)
            {
                Eigen::Vector3f Owp = pParent->GetCameraCenter();
                glVertex3f(Ow(0),Ow(1),Ow(2));
                glVertex3f(Owp(0),Owp(1),Owp(2));
            }

            // Loops
            set<KeyFrame*> sLoopKFs = vpKFs[i]->GetLoopEdges();
            for(set<KeyFrame*>::iterator sit=sLoopKFs.begin(), send=sLoopKFs.end(); sit!=send; sit++)
            {
                if((*sit)->mnId<vpKFs[i]->mnId)
                    continue;
                Eigen::Vector3f Owl = (*sit)->GetCameraCenter();
                glVertex3f(Ow(0),Ow(1),Ow(2));
                glVertex3f(Owl(0),Owl(1),Owl(2));
            }
        }

        glEnd();
    }

    if(bDrawInertialGraph && pActiveMap->isImuInitialized())
    {
        glLineWidth(mGraphLineWidth);
        glColor4f(1.0f,0.0f,0.0f,0.6f);
        glBegin(GL_LINES);

        //Draw inertial links
        for(size_t i=0; i<vpKFs.size(); i++)
        {
            KeyFrame* pKFi = vpKFs[i];
            Eigen::Vector3f Ow = pKFi->GetCameraCenter();
            KeyFrame* pNext = pKFi->mNextKF;
            if(pNext)
            {
                Eigen::Vector3f Owp = pNext->GetCameraCenter();
                glVertex3f(Ow(0),Ow(1),Ow(2));
                glVertex3f(Owp(0),Owp(1),Owp(2));
            }
        }

        glEnd();
    }

    vector<Map*> vpMaps = mpAtlas->GetAllMaps();

    if(bDrawKF)
    {
        for(Map* pMap : vpMaps)
        {
            if(pMap == pActiveMap)
                continue;

            vector<KeyFrame*> vpKFs = pMap->GetAllKeyFrames();

            for(size_t i=0; i<vpKFs.size(); i++)
            {
                KeyFrame* pKF = vpKFs[i];
                Eigen::Matrix4f Twc = pKF->GetPoseInverse().matrix();
                unsigned int index_color = pKF->mnOriginMapId;

                glPushMatrix();

                glMultMatrixf((GLfloat*)Twc.data());

                if(!vpKFs[i]->GetParent()) // It is the first KF in the map
                {
                    glLineWidth(mKeyFrameLineWidth*5);
                    glColor3f(1.0f,0.0f,0.0f);
                    glBegin(GL_LINES);
                }
                else
                {
                    glLineWidth(mKeyFrameLineWidth);
                    glColor3f(mfFrameColors[index_color][0],mfFrameColors[index_color][1],mfFrameColors[index_color][2]);
                    glBegin(GL_LINES);
                }

                glVertex3f(0,0,0);
                glVertex3f(w,h,z);
                glVertex3f(0,0,0);
                glVertex3f(w,-h,z);
                glVertex3f(0,0,0);
                glVertex3f(-w,-h,z);
                glVertex3f(0,0,0);
                glVertex3f(-w,h,z);

                glVertex3f(w,h,z);
                glVertex3f(w,-h,z);

                glVertex3f(-w,h,z);
                glVertex3f(-w,-h,z);

                glVertex3f(-w,h,z);
                glVertex3f(w,h,z);

                glVertex3f(-w,-h,z);
                glVertex3f(w,-h,z);
                glEnd();

                glPopMatrix();
            }
        }
    }
}

void MapDrawer::DrawCurrentCamera(pangolin::OpenGlMatrix &Twc)
{
    const float &w = mCameraSize;
    const float h = w*0.75;
    const float z = w*0.6;

    glPushMatrix();

#ifdef HAVE_GLES
        glMultMatrixf(Twc.m);
#else
        glMultMatrixd(Twc.m);
#endif

    glLineWidth(mCameraLineWidth);
    glColor3f(0.0f,1.0f,0.0f);
    glBegin(GL_LINES);
    glVertex3f(0,0,0);
    glVertex3f(w,h,z);
    glVertex3f(0,0,0);
    glVertex3f(w,-h,z);
    glVertex3f(0,0,0);
    glVertex3f(-w,-h,z);
    glVertex3f(0,0,0);
    glVertex3f(-w,h,z);

    glVertex3f(w,h,z);
    glVertex3f(w,-h,z);

    glVertex3f(-w,h,z);
    glVertex3f(-w,-h,z);

    glVertex3f(-w,h,z);
    glVertex3f(w,h,z);

    glVertex3f(-w,-h,z);
    glVertex3f(w,-h,z);
    glEnd();

    glPopMatrix();
}


void MapDrawer::SetCurrentCameraPose(const Sophus::SE3f &Tcw)
{
    unique_lock<mutex> lock(mMutexCamera);
    mCameraPose = Tcw.inverse();
}

// 【新增】带时间戳的版本
void MapDrawer::SetCurrentCameraPose(const Sophus::SE3f &Tcw, const double &timestamp)
{
    {
        unique_lock<mutex> lock(mMutexCamera);
        mCameraPose = Tcw.inverse();
        mCurrentTimestamp = timestamp;
    }

    // 【修复】在设置相机位姿后立即尝试记录距离信息
    RecordCurrentFrameDistances();
}

void MapDrawer::GetCurrentOpenGLCameraMatrix(pangolin::OpenGlMatrix &M, pangolin::OpenGlMatrix &MOw)
{
    Eigen::Matrix4f Twc;
    {
        unique_lock<mutex> lock(mMutexCamera);
        Twc = mCameraPose.matrix();
    }

    for (int i = 0; i<4; i++) {
        M.m[4*i] = Twc(0,i);
        M.m[4*i+1] = Twc(1,i);
        M.m[4*i+2] = Twc(2,i);
        M.m[4*i+3] = Twc(3,i);
    }

    MOw.SetIdentity();
    MOw.m[12] = Twc(0,3);
    MOw.m[13] = Twc(1,3);
    MOw.m[14] = Twc(2,3);
}

// 【新增】获取当前相机位置
Eigen::Vector3f MapDrawer::getCurrentCameraPosition()
{
    // 从当前相机位姿矩阵中提取位置
    Eigen::Matrix4f Twc;
    {
        unique_lock<mutex> lock(mMutexCamera);
        Twc = mCameraPose.matrix();
    }

    // 检查位姿是否有效
    if(!std::isfinite(Twc(0,3)) || !std::isfinite(Twc(1,3)) || !std::isfinite(Twc(2,3))) {
        return Eigen::Vector3f(0.0f, 0.0f, 0.0f);  // 返回原点作为默认值
    }

    return Eigen::Vector3f(Twc(0,3), Twc(1,3), Twc(2,3));
}

// 【新增】检查地图点是否在视锥体内
bool MapDrawer::isPointInFrustum(MapPoint* pMP, const Sophus::SE3f& Tcw, float viewingCosLimit)
{
    if(!pMP || pMP->isBad()) {
        return false;
    }

    // 获取地图点世界坐标
    Eigen::Vector3f P = pMP->GetWorldPos();

    // 转换到相机坐标系
    Eigen::Matrix3f Rcw = Tcw.rotationMatrix();
    Eigen::Vector3f tcw = Tcw.translation();
    Eigen::Vector3f Pc = Rcw * P + tcw;

    // 1. 检查深度（必须在相机前方）
    if(Pc(2) <= 0.0f) {
        return false;
    }

    // 2. 投影到图像平面并检查是否在图像边界内
    // 使用从配置文件读取的相机参数
    float u = mCameraFx * Pc(0) / Pc(2) + mCameraCx;
    float v = mCameraFy * Pc(1) / Pc(2) + mCameraCy;

    // 图像边界检查（留一些边界余量）
    float margin = 50.0f;  // 适当增加边界余量
    if(u < margin || u > (mCameraWidth - margin) || v < margin || v > (mCameraHeight - margin)) {
        return false;
    }

    // 3. 距离范围检查（基于地图点的尺度不变性）
    float maxDistance = pMP->GetMaxDistanceInvariance();
    float minDistance = pMP->GetMinDistanceInvariance();
    Eigen::Vector3f cameraPos = -Rcw.transpose() * tcw;  // 相机在世界坐标系中的位置
    Eigen::Vector3f PO = P - cameraPos;
    float dist = PO.norm();

    if(dist < minDistance || dist > maxDistance) {
        return false;
    }

    // 4. 视角检查（观察角度不能太大）
    Eigen::Vector3f Pn = pMP->GetNormal();
    float viewCos = PO.dot(Pn) / dist;

    if(viewCos < viewingCosLimit) {
        return false;
    }

    return true;
}

// 【新增】获取当前帧可见地图点
std::set<MapPoint*> MapDrawer::getCurrentFrameVisiblePoints()
{
    std::set<MapPoint*> visiblePoints;

    Map* pActiveMap = mpAtlas->GetCurrentMap();
    if(!pActiveMap) {
        return visiblePoints;
    }

    // 获取当前相机位姿
    Sophus::SE3f Tcw;
    {
        unique_lock<mutex> lock(mMutexCamera);
        Tcw = mCameraPose.inverse();  // mCameraPose是Twc，需要转换为Tcw
    }

    // 检查位姿是否有效
    Eigen::Vector3f translation = Tcw.translation();
    if(!std::isfinite(translation(0)) || !std::isfinite(translation(1)) || !std::isfinite(translation(2))) {
        return visiblePoints;
    }

    // 获取所有地图点进行可见性检查
    const vector<MapPoint*> &vpMPs = pActiveMap->GetAllMapPoints();

    // 视角限制：cos(60°) = 0.5
    float viewingCosLimit = 0.5f;

    for(MapPoint* pMP : vpMPs) {
        if(isPointInFrustum(pMP, Tcw, viewingCosLimit)) {
            visiblePoints.insert(pMP);
        }
    }

    return visiblePoints;
}

// 【新增】计算可见地图点的距离范围
MapDrawer::DistanceRange MapDrawer::calculateVisiblePointsDistanceRange(
    const std::set<MapPoint*>& visiblePoints,
    const Eigen::Vector3f& cameraPos)
{
    DistanceRange distRange;

    if(visiblePoints.empty()) {
        return distRange;
    }

    float minDist = FLT_MAX;
    float maxDist = 0.0f;

    for(MapPoint* pMP : visiblePoints) {
        if(!pMP || pMP->isBad()) continue;

        Eigen::Vector3f pos = pMP->GetWorldPos();
        float distance = (pos - cameraPos).norm();

        minDist = std::min(minDist, distance);
        maxDist = std::max(maxDist, distance);
    }

    distRange.minDistance = minDist;
    distRange.maxDistance = maxDist;
    distRange.range = maxDist - minDist;

    return distRange;
}

// 【新增】根据相对距离设置可见地图点颜色 - 靛紫到春绿渐变版本
void MapDrawer::setVisiblePointColorByRelativeDistance(float distance, const DistanceRange& distRange)
{
    // 如果距离范围无效或距离无效，使用默认颜色
    if(!distRange.isValid() || distance <= 0.0f || !std::isfinite(distance)) {
        glColor3f(0.5f, 0.5f, 0.5f);  // 中灰色
        return;
    }

    // 计算相对位置 (0.0 到 1.0)
    float relativePosition = (distance - distRange.minDistance) / distRange.range;

    // 确保在有效范围内
    relativePosition = std::max(0.0f, std::min(1.0f, relativePosition));

    // 【修改】靛紫到春绿渐变：最近的点显示靛紫色，最远的点显示春绿色
    // relativePosition = 0.0 (最近) -> 靛紫色 #4B0082 (RGB: 75/255, 0/255, 130/255)
    // relativePosition = 1.0 (最远) -> 春绿色 #00FF7F (RGB: 0/255, 255/255, 127/255)

    // 将十六进制颜色转换为0-1范围的RGB值
    float nearR = 75.0f / 255.0f;   // 靛紫色 R
    float nearG = 0.0f / 255.0f;    // 靛紫色 G
    float nearB = 130.0f / 255.0f;  // 靛紫色 B

    float farR = 0.0f / 255.0f;     // 春绿色 R
    float farG = 255.0f / 255.0f;   // 春绿色 G
    float farB = 127.0f / 255.0f;   // 春绿色 B

    // 线性插值计算当前颜色
    float r = nearR + relativePosition * (farR - nearR);
    float g = nearG + relativePosition * (farG - nearG);
    float b = nearB + relativePosition * (farB - nearB);

    glColor3f(r, g, b);
}

// 【新增】开始距离记录
void MapDrawer::StartDistanceRecording(const std::string& filename)
{
    mDistanceFile.open(filename);
    if(mDistanceFile.is_open()) {
        WriteFileHeader();
        mbRecordDistances = true;
        mCurrentFrameId = 0;
        std::cout << "Started distance recording to: " << filename << std::endl;
    } else {
        std::cerr << "Failed to open distance file: " << filename << std::endl;
    }
}

// 【新增】停止距离记录
void MapDrawer::StopDistanceRecording()
{
    mbRecordDistances = false;
    if(mDistanceFile.is_open()) {
        mDistanceFile.close();
        std::cout << "Distance recording stopped." << std::endl;
    }
}

// 【新增】写入文件头
void MapDrawer::WriteFileHeader()
{
    mDistanceFile << "# ORB-SLAM3 Distance Recording File" << std::endl;
    mDistanceFile << "# Format: Frame_ID Timestamp Camera_X Camera_Y Camera_Z MapPoint_ID MapPoint_X MapPoint_Y MapPoint_Z Pixel_Coordinates Distance" << std::endl;
    mDistanceFile << "# Pixel_Coordinates format: normalized_u,normalized_v (range [0,1])" << std::endl;
    mDistanceFile << "# Distances are sorted from nearest to farthest for each frame" << std::endl;

    // 添加时间戳
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    mDistanceFile << "# Generated on: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << std::endl;
}

// 【新增】获取当前时间戳
double MapDrawer::getCurrentTimestamp()
{
    unique_lock<mutex> lock(mMutexCamera);
    return mCurrentTimestamp;
}

// 【新增】记录当前帧距离信息
void MapDrawer::RecordCurrentFrameDistances()
{
    if(!mbRecordDistances || !mDistanceFile.is_open()) return;

    // 获取当前时间戳
    double timestamp = getCurrentTimestamp();

    // 【修复】检查时间戳是否发生变化，避免重复记录同一帧
    static double lastTimestamp = -1.0;
    if(timestamp == lastTimestamp) {
        return; // 相同时间戳，跳过记录
    }
    lastTimestamp = timestamp;

    // 获取当前帧信息
    Eigen::Vector3f currentCameraPos = getCurrentCameraPosition();
    std::set<MapPoint*> visiblePoints = getCurrentFrameVisiblePoints();

    if(visiblePoints.empty()) return;

    // 获取当前相机位姿用于投影计算
    Sophus::SE3f Tcw;
    {
        unique_lock<mutex> lock(mMutexCamera);
        Tcw = mCameraPose.inverse();  // mCameraPose是Twc，需要转换为Tcw
    }

    // 计算所有可见地图点的距离和像素坐标
    std::vector<MapPointDistance> distances;
    for(MapPoint* pMP : visiblePoints) {
        if(!pMP || pMP->isBad()) continue;

        MapPointDistance mpDist;
        mpDist.pMP = pMP;

        Eigen::Vector3f pos = pMP->GetWorldPos();
        mpDist.distance = (pos - currentCameraPos).norm();

        // 检查距离是否有效
        if(!std::isfinite(mpDist.distance) || mpDist.distance <= 0.0f) continue;

        distances.push_back(mpDist);
    }

    // 按距离从小到大排序
    std::sort(distances.begin(), distances.end());

    // 写入文件 - 格式：Frame_ID Timestamp Camera_X Camera_Y Camera_Z MapPoint_ID MapPoint_X MapPoint_Y MapPoint_Z Pixel_Coordinates Distance
    for(const auto& mpDist : distances) {
        Eigen::Vector3f mpPos = mpDist.pMP->GetWorldPos();

        // 计算归一化像素坐标
        std::pair<float, float> normalizedPixel = projectToNormalizedPixel(mpPos, Tcw);

        mDistanceFile << mCurrentFrameId << " "
                     << std::fixed << std::setprecision(6) << timestamp << " "
                     << std::setprecision(6) << currentCameraPos(0) << " "
                     << currentCameraPos(1) << " "
                     << currentCameraPos(2) << " "
                     << mpDist.pMP->mnId << " "
                     << mpPos(0) << " "
                     << mpPos(1) << " "
                     << mpPos(2) << " "
                     << std::setprecision(3) << normalizedPixel.first << "," << normalizedPixel.second << " "
                     << std::setprecision(6) << mpDist.distance << std::endl;
    }

    mDistanceFile.flush(); // 立即写入磁盘
    mCurrentFrameId++; // 【修复】只有在时间戳变化时才增加帧ID
}

// 【新增】加载相机参数
void MapDrawer::LoadCameraParameters(cv::FileStorage& fSettings)
{
    // 读取相机内参
    mCameraFx = fSettings["Camera.fx"];
    mCameraFy = fSettings["Camera.fy"];
    mCameraCx = fSettings["Camera.cx"];
    mCameraCy = fSettings["Camera.cy"];

    // 读取原始图像尺寸
    mOriginalImageWidth = fSettings["Camera.width"];
    mOriginalImageHeight = fSettings["Camera.height"];

    std::cout << "Loaded camera parameters for distance recording:" << std::endl;
    std::cout << "  fx=" << mCameraFx << ", fy=" << mCameraFy << std::endl;
    std::cout << "  cx=" << mCameraCx << ", cy=" << mCameraCy << std::endl;
    std::cout << "  Original image size: " << mOriginalImageWidth << "x" << mOriginalImageHeight << std::endl;
}

// 【新增】将3D点投影到归一化像素坐标
std::pair<float, float> MapDrawer::projectToNormalizedPixel(const Eigen::Vector3f& worldPos, const Sophus::SE3f& Tcw)
{
    // 将世界坐标转换到相机坐标系
    Eigen::Vector3f cameraPos = Tcw * worldPos;

    // 检查深度是否有效
    if(cameraPos(2) <= 0.0f) {
        return std::make_pair(-1.0f, -1.0f); // 无效坐标
    }

    // 投影到像素坐标
    float u = mCameraFx * cameraPos(0) / cameraPos(2) + mCameraCx;
    float v = mCameraFy * cameraPos(1) / cameraPos(2) + mCameraCy;

    // 归一化到[0,1]范围
    float normalized_u = u / mOriginalImageWidth;
    float normalized_v = v / mOriginalImageHeight;

    return std::make_pair(normalized_u, normalized_v);
}

// 【新增】根据绝对距离设置地图点颜色
void MapDrawer::setVisiblePointColorByAbsoluteDistance(float distance)
{
    // 颜色定义：靛紫色到春绿色的渐变
    // 靛紫色 (Indigo): RGB(75, 0, 130) -> OpenGL(0.294, 0.0, 0.510)
    // 春绿色 (Spring Green): RGB(0, 255, 127) -> OpenGL(0.0, 1.0, 0.498)

    float ratio;

    if(distance <= mMinDistance) {
        // 距离小于等于最小距离：使用靛紫色
        ratio = 0.0f;
    } else if(distance >= mMaxDistance) {
        // 距离大于等于最大距离：使用春绿色
        ratio = 1.0f;
    } else {
        // 在范围内：线性插值
        ratio = (distance - mMinDistance) / (mMaxDistance - mMinDistance);
    }

    // 线性插值计算颜色
    float r = 0.294f * (1.0f - ratio) + 0.0f * ratio;      // 红色分量
    float g = 0.0f * (1.0f - ratio) + 1.0f * ratio;        // 绿色分量
    float b = 0.510f * (1.0f - ratio) + 0.498f * ratio;    // 蓝色分量

    glColor3f(r, g, b);
}

} //namespace ORB_SLAM
