%YAML:1.0

#--------------------------------------------------------------------------------------------
# System config
#--------------------------------------------------------------------------------------------

# When the variables are commented, the system doesn't load a previous session or not store the current one

# If the LoadFile doesn't exist, the system give a message and create a new Atlas from scratch
#System.LoadAtlasFromFile: "Session_MH01_MH02_MH03_Stereo60_Pseudo"

# The store file is created from the current session, if a file with the same name exists it is deleted
#System.SaveAtlasToFile: "Session_MH01_MH02_MH03_Stereo60_Pseudo"

#--------------------------------------------------------------------------------------------
# Camera Parameters. Adjust them!
#--------------------------------------------------------------------------------------------
File.version: "1.0"

Camera.type: "PinHole"

# Camera calibration and distortion parameters (OpenCV)
# Both cameras use the same intrinsics from Dataset8.yaml (12mm long focal length camera)
Camera1.fx: 4298.5
Camera1.fy: 4291.4
Camera1.cx: 960.0
Camera1.cy: 540.0

# Distortion parameters from Dataset8.yaml
Camera1.k1: -0.28340811
Camera1.k2: 0.07395907
Camera1.p1: 0.00019359
Camera1.p2: 1.76187114e-05

# Camera2 uses the same intrinsics as Camera1 (equivalent after cropping)
Camera2.fx: 1343.3
Camera2.fy: 1390.4 
Camera2.cx: 300.0  # Adjusted for 600x350 cropped image (600/2 = 300)
Camera2.cy: 175.0  # Adjusted for 600x350 cropped image (350/2 = 175)

# Camera2 distortion parameters - may need calibration for the 3.9mm lens
# Start with same values as Camera1, but should be re-calibrated
Camera2.k1: -0.28340811
Camera2.k2: 0.07395907
Camera2.p1: 0.00019359
Camera2.p2: 1.76187114e-05

# Image dimensions for cropped cam1 images
Camera.width: 600
Camera.height: 350

# Camera frames per second
Camera.fps: 15

# Color order of the images (0: BGR, 1: RGB. It is ignored if images are grayscale)
Camera.RGB: 1

#--------------------------------------------------------------------------------------------
# Distance Visualization Parameters
#--------------------------------------------------------------------------------------------

# Distance visualization mode (true: absolute distance, false: relative distance)
DistanceVisualization.useAbsoluteDistance: false

# Absolute distance range for color mapping (in meters)
DistanceVisualization.minDistance: 1.0
DistanceVisualization.maxDistance: 10.0

# Stereo depth threshold (adjust based on your baseline distance)
Stereo.ThDepth: 60.0

# Transformation matrix from cam0 to cam1 (T_c1_c2)
# This matrix MUST be calibrated based on your actual camera setup!
# The current values are placeholders - you need to measure/calibrate:
# - Baseline distance between cameras
# - Relative rotation between cameras
# - Any vertical offset between cameras
Stereo.T_c1_c2: !!opencv-matrix
  rows: 4
  cols: 4
  dt: f
  # PLACEHOLDER VALUES - MUST BE CALIBRATED FOR YOUR SETUP!
  # Assuming cameras are horizontally aligned with baseline ~0.12m (120mm)
  # You MUST replace these with your actual calibrated values
  data: [1.0, 0.0, 0.0, 0.12,
         0.0, 1.0, 0.0, 0.0,
         0.0, 0.0, 1.0, 0.0,
         0.0, 0.0, 0.0, 1.0]

#--------------------------------------------------------------------------------------------
# ORB Parameters
#--------------------------------------------------------------------------------------------

# ORB Extractor: Number of features per image
ORBextractor.nFeatures: 1200

# ORB Extractor: Scale factor between levels in the scale pyramid 	
ORBextractor.scaleFactor: 1.2

# ORB Extractor: Number of levels in the scale pyramid	
ORBextractor.nLevels: 8

# ORB Extractor: Fast threshold
# Image is divided in a grid. At each cell FAST are extracted imposing a minimum response.
# Firstly we impose iniThFAST. If no corners are detected we impose a lower value minThFAST
# You can lower these values if your images have low contrast			
ORBextractor.iniThFAST: 20
ORBextractor.minThFAST: 7

#--------------------------------------------------------------------------------------------
# Viewer Parameters
#--------------------------------------------------------------------------------------------
Viewer.KeyFrameSize: 0.05
Viewer.KeyFrameLineWidth: 1.0
Viewer.GraphLineWidth: 0.9
Viewer.PointSize: 2.0
Viewer.CameraSize: 0.08
Viewer.CameraLineWidth: 3.0
Viewer.ViewpointX: 0.0
Viewer.ViewpointY: -0.7
Viewer.ViewpointZ: -1.8
Viewer.ViewpointF: 500.0
Viewer.imageViewScale: 1.0

