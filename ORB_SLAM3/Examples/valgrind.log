==2363== Memcheck, a memory error detector
==2363== Copyright (C) 2002-2017, and GNU GPL'd, by <PERSON> et al.
==2363== Using Valgrind-3.15.0 and LibVEX; rerun with -h for copyright info
==2363== Command: ./Monocular/mono_euroc ../Vocabulary/ORBvoc.txt ./Monocular/Dataset17.yaml /Datasets/EuRoC/dataset17 ./Monocular/EuRoC_TimeStamps/dataset17.txt dataset-dataset17_mono
==2363== Parent PID: 0
==2363== 
vex amd64->IR: unhandled instruction bytes: 0x62 0xF1 0xFE 0x28 0x7F 0x84 0x24 0xA8 0x4 0x0
vex amd64->IR:   REX=0 REX.W=0 REX.R=0 REX.X=0 REX.B=0
vex amd64->IR:   VEX=0 VEX.L=0 VEX.nVVVV=0x0 ESC=NONE
vex amd64->IR:   PFX.66=0 PFX.F2=0 PFX.F3=0
==2363== valgrind: Unrecognised instruction at address 0x113d7a.
==2363==    at 0x113D7A: LoadImages(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >&, std::vector<double, std::allocator<double> >&) (in /ORB_SLAM3/Examples/Monocular/mono_euroc)
==2363==    by 0x112369: main (in /ORB_SLAM3/Examples/Monocular/mono_euroc)
==2363== Your program just tried to execute an instruction that Valgrind
==2363== did not recognise.  There are two possible reasons for this.
==2363== 1. Your program has a bug and erroneously jumped to a non-code
==2363==    location.  If you are running Memcheck and you just saw a
==2363==    warning about a bad jump, it's probably your program's fault.
==2363== 2. The instruction is legitimate but Valgrind doesn't handle it,
==2363==    i.e. it's Valgrind's fault.  If you think this is the case or
==2363==    you are not sure, please let us know and we'll try to fix it.
==2363== Either way, Valgrind will now raise a SIGILL signal which will
==2363== probably kill your program.
==2363== 
==2363== Process terminating with default action of signal 4 (SIGILL): dumping core
==2363==  Illegal opcode at address 0x113D7A
==2363==    at 0x113D7A: LoadImages(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >&, std::vector<double, std::allocator<double> >&) (in /ORB_SLAM3/Examples/Monocular/mono_euroc)
==2363==    by 0x112369: main (in /ORB_SLAM3/Examples/Monocular/mono_euroc)
==2363== 
==2363== HEAP SUMMARY:
==2363==     in use at exit: 253,752 bytes in 2,098 blocks
==2363==   total heap usage: 2,867 allocs, 769 frees, 456,990 bytes allocated
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 120 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642DDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621EDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 121 of 549
==2363==    at 0x483B723: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0x483E017: realloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3F3F: g_realloc (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642D57: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621EDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 122 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642DDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621F41: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 123 of 549
==2363==    at 0x483B723: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0x483E017: realloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3F3F: g_realloc (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642D57: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621F41: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 124 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642DDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB62BFFF: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C1: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 125 of 549
==2363==    at 0x483B723: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0x483E017: realloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3F3F: g_realloc (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642D57: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB62BFFF: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C1: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 126 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642DDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB626CC3: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C6: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 16 bytes in 1 blocks are possibly lost in loss record 127 of 549
==2363==    at 0x483B723: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0x483E017: realloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3F3F: g_realloc (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642D57: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460D6: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB626CC3: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C6: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 80 bytes in 1 blocks are possibly lost in loss record 353 of 549
==2363==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xAB09356: ??? (in /usr/lib/x86_64-linux-gnu/libGLdispatch.so.0.0.0)
==2363==    by 0xAB096B4: __glDispatchInit (in /usr/lib/x86_64-linux-gnu/libGLdispatch.so.0.0.0)
==2363==    by 0x8CE978C: ??? (in /usr/lib/x86_64-linux-gnu/libGLX.so.0.0.0)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363==    by 0x1FFF000D22: ???
==2363==    by 0x1FFF000D3D: ???
==2363== 
==2363== 80 bytes in 1 blocks are possibly lost in loss record 354 of 549
==2363==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xAB09356: ??? (in /usr/lib/x86_64-linux-gnu/libGLdispatch.so.0.0.0)
==2363==    by 0xAB098A0: __glDispatchRegisterStubCallbacks (in /usr/lib/x86_64-linux-gnu/libGLdispatch.so.0.0.0)
==2363==    by 0xABC4209: ??? (in /usr/lib/x86_64-linux-gnu/libGL.so.1.7.0)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363==    by 0x1FFF000D22: ???
==2363==    by 0x1FFF000D3D: ???
==2363== 
==2363== 80 bytes in 1 blocks are possibly lost in loss record 355 of 549
==2363==    at 0x483B7F3: malloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xAB09356: ??? (in /usr/lib/x86_64-linux-gnu/libGLdispatch.so.0.0.0)
==2363==    by 0xAB098A0: __glDispatchRegisterStubCallbacks (in /usr/lib/x86_64-linux-gnu/libGLdispatch.so.0.0.0)
==2363==    by 0x7B041E9: ??? (in /usr/lib/x86_64-linux-gnu/libOpenGL.so.0.0.0)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363==    by 0x1FFF000D22: ???
==2363==    by 0x1FFF000D3D: ???
==2363== 
==2363== 96 bytes in 1 blocks are possibly lost in loss record 406 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB6420E7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB64228A: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61AFDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363==    by 0x1FFF000D22: ???
==2363== 
==2363== 96 bytes in 1 blocks are possibly lost in loss record 407 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB6420E7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB64228A: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460C8: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621EDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 96 bytes in 1 blocks are possibly lost in loss record 408 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB6420E7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB64228A: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460C8: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621F41: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 96 bytes in 1 blocks are possibly lost in loss record 409 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB6420E7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB64228A: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460C8: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB62BFFF: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C1: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 96 bytes in 1 blocks are possibly lost in loss record 410 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB6420E7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB64228A: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6460C8: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB626CC3: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C6: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== 132 bytes in 1 blocks are possibly lost in loss record 433 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB6430F4: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB646159: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621EDE: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 132 bytes in 1 blocks are possibly lost in loss record 434 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB6430F4: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB646159: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB621F41: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0B7: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 148 bytes in 1 blocks are possibly lost in loss record 451 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642F08: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB646159: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB62BFFF: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C1: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 148 bytes in 1 blocks are possibly lost in loss record 452 of 549
==2363==    at 0x483DD99: calloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3EF0: g_malloc0 (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642F08: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB646159: g_type_register_fundamental (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB626CC3: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0C6: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363==    by 0x1FFF000D09: ???
==2363== 
==2363== 184 bytes in 1 blocks are possibly lost in loss record 455 of 549
==2363==    at 0x483DFAF: realloc (in /usr/lib/x86_64-linux-gnu/valgrind/vgpreload_memcheck-amd64-linux.so)
==2363==    by 0xB6C3F3F: g_realloc (in /usr/lib/x86_64-linux-gnu/libglib-2.0.so.0.6400.6)
==2363==    by 0xB642063: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB646324: g_type_register_static (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB62DD12: g_param_type_register_static (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB6307EA: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0xB61B0CB: ??? (in /usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0.6400.6)
==2363==    by 0x4011B99: call_init.part.0 (dl-init.c:72)
==2363==    by 0x4011CA0: call_init (dl-init.c:30)
==2363==    by 0x4011CA0: _dl_init (dl-init.c:119)
==2363==    by 0x4001139: ??? (in /usr/lib/x86_64-linux-gnu/ld-2.31.so)
==2363==    by 0x5: ???
==2363==    by 0x1FFF000CF2: ???
==2363== 
==2363== LEAK SUMMARY:
==2363==    definitely lost: 0 bytes in 0 blocks
==2363==    indirectly lost: 0 bytes in 0 blocks
==2363==      possibly lost: 1,592 bytes in 21 blocks
==2363==    still reachable: 252,160 bytes in 2,077 blocks
==2363==                       of which reachable via heuristic:
==2363==                         newarray           : 1,536 bytes in 16 blocks
==2363==         suppressed: 0 bytes in 0 blocks
==2363== Reachable blocks (those to which a pointer was found) are not shown.
==2363== To see them, rerun with: --leak-check=full --show-leak-kinds=all
==2363== 
==2363== For lists of detected and suppressed errors, rerun with: -s
==2363== ERROR SUMMARY: 21 errors from 21 contexts (suppressed: 0 from 0)
