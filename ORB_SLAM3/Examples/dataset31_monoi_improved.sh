#!/bin/bash
pathDatasetEuroc='/Datasets/EuRoC' #Example, it is necesary to change it by the dataset path

#------------------------------------
# Monocular-Inertial Examples with improved settings
echo "Launching dataset31 with Monocular-Inertial sensor (Improved Settings)"
echo "Improvements:"
echo "  - Lower FAST thresholds for better feature extraction"
echo "  - More features (1500 vs 1000)"
echo "  - Fast IMU initialization enabled"
echo "  - Better error handling"

# Check if dataset exists
if [ ! -d "$pathDatasetEuroc/dataset31" ]; then
    echo "Error: Dataset directory $pathDatasetEuroc/dataset31 not found!"
    exit 1
fi

# Check if required files exist
if [ ! -f "./Monocular-Inertial/Dataset31.yaml" ]; then
    echo "Error: Configuration file ./Monocular-Inertial/Dataset31.yaml not found!"
    exit 1
fi

if [ ! -f "./Monocular-Inertial/EuRoC_TimeStamps/dataset31.txt" ]; then
    echo "Error: Timestamp file ./Monocular-Inertial/EuRoC_TimeStamps/dataset31.txt not found!"
    exit 1
fi

# Run with improved settings
./Monocular-Inertial/mono_inertial_euroc ../Vocabulary/ORBvoc.txt ./Monocular-Inertial/Dataset31.yaml "$pathDatasetEuroc"/dataset31 ./Monocular-Inertial/EuRoC_TimeStamps/dataset31.txt dataset-dataset31_monoi_improved

echo "Execution completed. Check the output files:"
echo "  - f_dataset-dataset31_monoi_improved.txt (trajectory)"
echo "  - distances_dataset-dataset31_monoi_improved.txt (distance data)"
