%YAML:1.0

#--------------------------------------------------------------------------------------------
# System config
#--------------------------------------------------------------------------------------------

# When the variables are commented, the system doesn't load a previous session or not store the current one

# If the LoadFile doesn't exist, the system give a message and create a new Atlas from scratch
#System.LoadAtlasFromFile: "Session_MH01_MH02_MH03_Mono"

# The store file is created from the current session, if a file with the same name exists it is deleted
#System.SaveAtlasToFile: "Session_MH01_MH02_MH03_Mono"

#--------------------------------------------------------------------------------------------
# Camera Parameters. Adjust them!
#--------------------------------------------------------------------------------------------
File.version: "1.0"

Camera.type: "KannalaBrandt8"

# Camera calibration and distortion parameters (OpenCV) 
Camera1.fx: 571.8238272397848
Camera1.fy: 571.2029504647904
Camera1.cx: 304.82338044540444
Camera1.cy: 393.8984425538716

#old
Camera.fx: 571.8238272397848
Camera.fy: 571.2029504647904
Camera.cx: 304.82338044540444
Camera.cy: 393.8984425538716

Camera1.k1: 0.3932012083340933
Camera1.k2: -0.5766466787082557
Camera1.k3: 4.577737163203353
Camera1.k4: -8.854355560266951

#old
Camera.k1: 0.3932012083340933
Camera.k2: -0.5766466787082557
Camera.k3: 4.577737163203353
Camera.k4: -8.854355560266951

Camera.width: 600
Camera.height: 800

Camera.newWidth: 450
Camera.newHeight: 600

# Camera frames per second
Camera.fps: 15

# Color order of the images (0: BGR, 1: RGB. It is ignored if images are grayscale)
Camera.RGB: 1

#--------------------------------------------------------------------------------------------
# Distance Visualization Parameters
#--------------------------------------------------------------------------------------------

# Distance visualization mode (true: absolute distance, false: relative distance)
DistanceVisualization.useAbsoluteDistance: false

# Absolute distance range for color mapping (in meters)
DistanceVisualization.minDistance: 1.0
DistanceVisualization.maxDistance: 10.0

# Transformation from camera to body-frame (imu)
# Camera is 13cm in front of IMU, tilted 30° upward
# IMU: X-up Y-right Z-forward, Camera: X-right Y-down Z-forward
# R_total = R_align × R_tilt, t = [0, 0, 0.13]
# R_align: [0 -1 0; 1 0 0; 0 0 1], R_tilt: [1 0 0; 0 0.866 -0.5; 0 0.5 0.866]
IMU.T_b_c1: !!opencv-matrix
   rows: 4
   cols: 4
   dt: f
   data: [0.99984942, -0.0136002, 0.01077818, 0.02149114,
         -0.01375209, -0.99980537, 0.01414582, 0.00068677,
         0.0105837, -0.01429192, -0.99984185, 0.00209197,
         0.0, 0.0, 0.0, 1.0]

# IMU noise
IMU.NoiseGyro: 1.100e-3
IMU.NoiseAcc: 1.217e-2
IMU.GyroWalk: 2.293e-5
IMU.AccWalk: 1.485e-3
IMU.Frequency: 421.0
IMU.fastInit: 1

#--------------------------------------------------------------------------------------------
# ORB Parameters
#--------------------------------------------------------------------------------------------

# ORB Extractor: Number of features per image
ORBextractor.nFeatures: 1500

# ORB Extractor: Scale factor between levels in the scale pyramid 	
ORBextractor.scaleFactor: 1.2

# ORB Extractor: Number of levels in the scale pyramid	
ORBextractor.nLevels: 8

# ORB Extractor: Fast threshold
# Image is divided in a grid. At each cell FAST are extracted imposing a minimum response.
# Firstly we impose iniThFAST. If no corners are detected we impose a lower value minThFAST
# You can lower these values if your images have low contrast			
ORBextractor.iniThFAST: 12
ORBextractor.minThFAST: 5

#--------------------------------------------------------------------------------------------
# Viewer Parameters
#---------------------------------------------------------------------------------------------
Viewer.KeyFrameSize: 0.05
Viewer.KeyFrameLineWidth: 1.0
Viewer.GraphLineWidth: 0.9
Viewer.PointSize: 2.0
Viewer.CameraSize: 0.08
Viewer.CameraLineWidth: 3.0
Viewer.ViewpointX: 0.0
Viewer.ViewpointY: -0.7
Viewer.ViewpointZ: -1.8
Viewer.ViewpointF: 500.0

