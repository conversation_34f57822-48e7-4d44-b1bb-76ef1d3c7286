#!/usr/bin/env python3
"""
Dataset31 分析脚本
分析IMU数据质量、相机数据和可能的跟踪失败原因
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import sys

def analyze_imu_data(imu_file):
    """分析IMU数据质量"""
    print("=== IMU数据分析 ===")
    
    if not os.path.exists(imu_file):
        print(f"错误：IMU文件 {imu_file} 不存在")
        return None
    
    # 读取IMU数据
    imu_data = pd.read_csv(imu_file, comment='#')
    timestamps = imu_data.iloc[:, 0].values
    gyro_x = imu_data.iloc[:, 1].values
    gyro_y = imu_data.iloc[:, 2].values
    gyro_z = imu_data.iloc[:, 3].values
    acc_x = imu_data.iloc[:, 4].values
    acc_y = imu_data.iloc[:, 5].values
    acc_z = imu_data.iloc[:, 6].values
    
    # 计算时间信息
    duration = (timestamps[-1] - timestamps[0]) / 1e9
    frequency = len(timestamps) / duration
    
    print(f"数据点数: {len(timestamps)}")
    print(f"时长: {duration:.2f} 秒")
    print(f"频率: {frequency:.2f} Hz")
    
    # 计算运动统计
    acc_magnitude = np.sqrt(acc_x**2 + acc_y**2 + acc_z**2)
    gyro_magnitude = np.sqrt(gyro_x**2 + gyro_y**2 + gyro_z**2)
    
    # 去除重力的加速度
    gravity = 9.81
    acc_without_gravity = np.abs(acc_magnitude - gravity)
    
    print(f"\n运动统计:")
    print(f"加速度幅值 (去重力): 平均={np.mean(acc_without_gravity):.3f}, 最大={np.max(acc_without_gravity):.3f} m/s²")
    print(f"角速度幅值: 平均={np.mean(gyro_magnitude):.3f}, 最大={np.max(gyro_magnitude):.3f} rad/s")
    
    # 检查激励运动
    sufficient_acc = np.mean(acc_without_gravity > 0.5) * 100
    sufficient_gyro = np.mean(gyro_magnitude > 0.1) * 100
    
    print(f"\n激励运动检测:")
    print(f"足够加速度激励 (>0.5 m/s²): {sufficient_acc:.1f}%")
    print(f"足够角速度激励 (>0.1 rad/s): {sufficient_gyro:.1f}%")
    
    # 检查数据间隔一致性
    intervals = np.diff(timestamps) / 1e9
    print(f"\n数据间隔统计:")
    print(f"平均间隔: {np.mean(intervals):.6f} 秒")
    print(f"间隔标准差: {np.std(intervals):.6f} 秒")
    print(f"间隔范围: [{np.min(intervals):.6f}, {np.max(intervals):.6f}] 秒")
    
    return {
        'duration': duration,
        'frequency': frequency,
        'acc_excitation': sufficient_acc,
        'gyro_excitation': sufficient_gyro,
        'interval_std': np.std(intervals)
    }

def analyze_camera_data(cam_file):
    """分析相机数据"""
    print("\n=== 相机数据分析 ===")
    
    if not os.path.exists(cam_file):
        print(f"错误：相机文件 {cam_file} 不存在")
        return None
    
    # 读取相机数据
    cam_data = pd.read_csv(cam_file, comment='#')
    timestamps = cam_data.iloc[:, 0].values
    
    # 计算时间信息
    duration = (timestamps[-1] - timestamps[0]) / 1e9
    frequency = len(timestamps) / duration
    
    print(f"图像数量: {len(timestamps)}")
    print(f"时长: {duration:.2f} 秒")
    print(f"频率: {frequency:.2f} Hz")
    
    # 检查时间间隔一致性
    intervals = np.diff(timestamps) / 1e9
    expected_interval = 1.0 / 15.0  # 15 Hz
    
    print(f"\n时间间隔统计:")
    print(f"期望间隔: {expected_interval:.6f} 秒")
    print(f"实际平均间隔: {np.mean(intervals):.6f} 秒")
    print(f"间隔标准差: {np.std(intervals):.6f} 秒")
    print(f"间隔范围: [{np.min(intervals):.6f}, {np.max(intervals):.6f}] 秒")
    
    return {
        'duration': duration,
        'frequency': frequency,
        'interval_std': np.std(intervals)
    }

def check_synchronization(imu_file, cam_file):
    """检查IMU和相机数据的同步性"""
    print("\n=== 同步性分析 ===")
    
    try:
        imu_data = pd.read_csv(imu_file, comment='#')
        cam_data = pd.read_csv(cam_file, comment='#')
        
        imu_start = imu_data.iloc[0, 0]
        imu_end = imu_data.iloc[-1, 0]
        cam_start = cam_data.iloc[0, 0]
        cam_end = cam_data.iloc[-1, 0]
        
        print(f"IMU时间范围: {imu_start} - {imu_end}")
        print(f"相机时间范围: {cam_start} - {cam_end}")
        
        # 检查时间重叠
        overlap_start = max(imu_start, cam_start)
        overlap_end = min(imu_end, cam_end)
        
        if overlap_start < overlap_end:
            overlap_duration = (overlap_end - overlap_start) / 1e9
            print(f"时间重叠: {overlap_duration:.2f} 秒")
            
            # 计算时间偏移
            time_offset = (cam_start - imu_start) / 1e9
            print(f"时间偏移 (相机-IMU): {time_offset:.6f} 秒")
        else:
            print("警告：IMU和相机数据没有时间重叠！")
            
    except Exception as e:
        print(f"同步性分析失败: {e}")

def generate_recommendations(imu_stats, cam_stats):
    """生成改进建议"""
    print("\n=== 改进建议 ===")
    
    recommendations = []
    
    if imu_stats and imu_stats['acc_excitation'] < 30:
        recommendations.append("1. 加速度激励不足 - 建议增加更多的平移运动")
    
    if cam_stats and cam_stats['frequency'] < 20:
        recommendations.append("2. 相机频率过低 - 考虑提高到20-30Hz")
    
    if imu_stats and imu_stats['interval_std'] > 0.001:
        recommendations.append("3. IMU时间间隔不稳定 - 检查数据采集系统")
    
    recommendations.extend([
        "4. 降低FAST阈值以提取更多特征点",
        "5. 启用快速IMU初始化模式",
        "6. 增加ORB特征点数量",
        "7. 检查相机内参标定精度"
    ])
    
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")

def main():
    """主函数"""
    dataset_path = "Datasets/EuRoC/dataset31/mav0"
    imu_file = os.path.join(dataset_path, "imu0/data.csv")
    cam_file = os.path.join(dataset_path, "cam0/data.csv")
    
    print("Dataset31 跟踪失败原因分析")
    print("=" * 50)
    
    # 分析IMU数据
    imu_stats = analyze_imu_data(imu_file)
    
    # 分析相机数据
    cam_stats = analyze_camera_data(cam_file)
    
    # 检查同步性
    check_synchronization(imu_file, cam_file)
    
    # 生成建议
    generate_recommendations(imu_stats, cam_stats)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
