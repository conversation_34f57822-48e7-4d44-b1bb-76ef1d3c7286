sudo: required
dist: xenial

before_install:
  - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then if ! [[ "$BUILD_TYPE" == "Python" ]]; then if ! [[ "$BUILD_TYPE" == "Docs" ]]; then scripts/install_linux_deps.sh ; fi ; fi ; fi
  - if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then scripts/install_osx_deps.sh ; fi

install:
- if [[ "$BUILD_TYPE" == "Coverage" ]]; then wget http://ftp.de.debian.org/debian/pool/main/l/lcov/lcov_1.11.orig.tar.gz ; fi
- if [[ "$BUILD_TYPE" == "Coverage" ]]; then tar xf lcov_1.11.orig.tar.gz ; fi
- if [[ "$BUILD_TYPE" == "Coverage" ]]; then sudo make -C lcov-1.11/ install ; fi
- if [[ "$BUILD_TYPE" == "Coverage" ]]; then gem install coveralls-lcov ; fi
- if [[ "$BUILD_TYPE" == "Python" ]]; then pip3 install sympy ; fi
- if [[ "$BUILD_TYPE" == "Docs" ]]; then scripts/install_docs_deps.sh ; fi

language: cpp

cache: ccache

matrix:
  include:
  - language: cpp
    compiler: gcc
    os: linux
    env: BUILD_TYPE=Coverage  ROW_MAJOR_DEFAULT=OFF
  - language: cpp
    compiler: gcc
    os: linux
    env: BUILD_TYPE=Release ROW_MAJOR_DEFAULT=OFF
  - language: cpp
    compiler: clang
    os: linux
    env: BUILD_TYPE=Debug ROW_MAJOR_DEFAULT=OFF
  - language: cpp
    compiler: gcc
    os: linux
    env: BUILD_TYPE=Debug ROW_MAJOR_DEFAULT=ON
  - language: cpp
    compiler: clang
    os: linux
    env: BUILD_TYPE=Release ROW_MAJOR_DEFAULT=OFF
  - language: cpp
    compiler: clang
    os: osx
    env: BUILD_TYPE=Debug
  - language: python
    env: BUILD_TYPE=Python
    os: linux
    python: "3.7"
  - language: python
    env: BUILD_TYPE=Docs
    os: linux
    python: "3.7"


script:
  - if ! [[ "$BUILD_TYPE" == "Python" ]]; then if ! [[ "$BUILD_TYPE" == "Docs" ]]; then scripts/run_cpp_tests.sh ; fi ; fi
  - if [[ "$BUILD_TYPE" == "Python" ]]; then cd py; ./run_tests.sh ; fi
  - if [[ "$BUILD_TYPE" == "Docs" ]]; then ./make_docs.sh ; fi
  - if [[ "$BUILD_TYPE" == "Docs" ]]; then cd html-dir ; fi
  - if [[ "$BUILD_TYPE" == "Docs" ]]; then touch .nojekyll ; fi
  - if [[ "$BUILD_TYPE" == "Docs" ]]; then cd ../.. ; fi

after_success:
 - if [[ "$BUILD_TYPE" == "Coverage" ]]; then lcov --directory . --capture --output-file coverage.info ; fi
 - if [[ "$BUILD_TYPE" == "Coverage" ]]; then lcov --remove coverage.info 'test/*' '/usr/*' --output-file coverage.info ; fi
 - if [[ "$BUILD_TYPE" == "Coverage" ]]; then lcov --list coverage.info ; fi
 - if [[ "$BUILD_TYPE" == "Coverage" ]]; then coveralls-lcov --repo-token ${COVERALLS_TOKEN} coverage.info ; fi

deploy:
  provider: pages
  skip_cleanup: true
  github_token: $GITHUB_TOKEN  # Set in the settings page of your repository, as a secure variable
  keep_history: false
  local_dir: html-dir/
  verbose: true
  on:
    branch: master
    condition: $BUILD_TYPE = Docs

branches:
  only:
    - master
