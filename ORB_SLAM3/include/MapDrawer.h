/**
* This file is part of ORB-SLAM3
*
* Copyright (C) 2017-2021 <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, University of Zaragoza.
* Copyright (C) 2014-2016 <PERSON><PERSON>, <PERSON> and <PERSON>, University of Zaragoza.
*
* ORB-SLAM3 is free software: you can redistribute it and/or modify it under the terms of the GNU General Public
* License as published by the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* ORB-SLAM3 is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even
* the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License along with ORB-SLAM3.
* If not, see <http://www.gnu.org/licenses/>.
*/


#ifndef MAPDRAWER_H
#define MAPDRAWER_H

#include"Atlas.h"
#include"MapPoint.h"
#include"KeyFrame.h"
#include "Settings.h"
#include<pangolin/pangolin.h>

#include<mutex>
#include<set>
#include<cfloat>

namespace ORB_SLAM3
{

class Settings;

class MapDrawer
{
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
    MapDrawer(Atlas* pAtlas, const string &strSettingPath, Settings* settings);

    void newParameterLoader(Settings* settings);

    Atlas* mpAtlas;

    void DrawMapPoints();
    void DrawKeyFrames(const bool bDrawKF, const bool bDrawGraph, const bool bDrawInertialGraph, const bool bDrawOptLba);
    void DrawCurrentCamera(pangolin::OpenGlMatrix &Twc);
    void SetCurrentCameraPose(const Sophus::SE3f &Tcw);
    void SetCurrentCameraPose(const Sophus::SE3f &Tcw, const double &timestamp);  // 【新增】带时间戳的版本
    void SetReferenceKeyFrame(KeyFrame *pKF);
    void GetCurrentOpenGLCameraMatrix(pangolin::OpenGlMatrix &M, pangolin::OpenGlMatrix &MOw);

    // 【新增】距离记录公共接口
    void StartDistanceRecording(const std::string& filename);
    void StopDistanceRecording();

private:
    // 【新增】距离范围结构体
    struct DistanceRange {
        float minDistance;
        float maxDistance;
        float range;

        DistanceRange() : minDistance(0.0f), maxDistance(0.0f), range(0.0f) {}

        bool isValid() const {
            return range > 0.001f;  // 避免除零错误
        }
    };

    bool ParseViewerParamFile(cv::FileStorage &fSettings);

    float mKeyFrameSize;
    float mKeyFrameLineWidth;
    float mGraphLineWidth;
    float mPointSize;
    float mCameraSize;
    float mCameraLineWidth;

    // 【新增】相机参数（用于可见性判断）
    float mCameraFx, mCameraFy;  // 焦距
    float mCameraCx, mCameraCy;  // 主点
    float mCameraWidth, mCameraHeight;  // 图像尺寸
    float mCameraHFov, mCameraVFov;  // 视场角（度）

    Sophus::SE3f mCameraPose;
    double mCurrentTimestamp;  // 【新增】当前帧时间戳

    std::mutex mMutexCamera;

    float mfFrameColors[6][3] = {{0.0f, 0.0f, 1.0f},
                                {0.8f, 0.4f, 1.0f},
                                {1.0f, 0.2f, 0.4f},
                                {0.6f, 0.0f, 1.0f},
                                {1.0f, 1.0f, 0.0f},
                                {0.0f, 1.0f, 1.0f}};

    // 【新增】距离记录相关数据结构
    struct MapPointDistance {
        MapPoint* pMP;
        float distance;

        bool operator<(const MapPointDistance& other) const {
            return distance < other.distance;
        }
    };

    // 【新增】距离记录相关成员变量
    std::ofstream mDistanceFile;
    bool mbRecordDistances;
    int mCurrentFrameId;

    // 【新增】原始图像尺寸（用于像素坐标计算）
    int mOriginalImageWidth, mOriginalImageHeight;

    // 【新增】距离可视化配置参数
    bool mbUseAbsoluteDistance;
    float mMinDistance, mMaxDistance;

    // 【新增】新增的函数
    Eigen::Vector3f getCurrentCameraPosition();
    bool isPointInFrustum(MapPoint* pMP, const Sophus::SE3f& Tcw, float viewingCosLimit);
    std::set<MapPoint*> getCurrentFrameVisiblePoints();
    DistanceRange calculateVisiblePointsDistanceRange(const std::set<MapPoint*>& visiblePoints, const Eigen::Vector3f& cameraPos);
    void setVisiblePointColorByRelativeDistance(float distance, const DistanceRange& distRange);
    void setVisiblePointColorByAbsoluteDistance(float distance);

    // 【新增】距离记录相关函数
    void RecordCurrentFrameDistances();
    void WriteFileHeader();
    double getCurrentTimestamp();

    // 【新增】像素坐标计算函数
    std::pair<float, float> projectToNormalizedPixel(const Eigen::Vector3f& worldPos, const Sophus::SE3f& Tcw);

    // 【新增】加载相机参数函数
    void LoadCameraParameters(cv::FileStorage& fSettings);

};

} //namespace ORB_SLAM

#endif // MAPDRAWER_H
